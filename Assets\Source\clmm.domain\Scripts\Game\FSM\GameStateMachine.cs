using Autofac.Annotation;
using Ock;

namespace CLMM.Domain
{
    [Component(AutofacScope = AutofacScope.InstancePerLifetimeScope)]
    public class GameStateMachine
    {
        [Autowired]
        public readonly IEventDispatcher EventDispatcher = null!;

        public GameState CurrentState { get; private set; }

        public void Start(GameState state)
        {
            CurrentState = state;

            var @event = new GameStateChangedEvent
            {
                EnterState = CurrentState,
                LeaveState = GameState.NONE
            };
            EventDispatcher.Dispatch(@event);
        }

        public void ChangeState(GameState state)
        {
            if (CurrentState == state)
                return;

            var lastState = CurrentState;
            CurrentState = state;

            var @event = new GameStateChangedEvent
            {
                EnterState = CurrentState,
                LeaveState = lastState
            };
            EventDispatcher.Dispatch(@event);
        }
    }

}